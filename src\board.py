from abc import ABC, abstractmethod
from base import NamedItem, Point, Dimension, Side, Rotation
from surface import Surface
from box import Box
from cylinder import <PERSON><PERSON><PERSON>
from dowel import LocatedDowel
from enum import Enum
from dataclasses import dataclass, field
from board_hole import Hole, FrontHole, BackHole, LeftHole, RightHole, TopHole, BottomHole
from board_groove import Groove


class BandingType(Enum):
    """Represents the type of banding."""
    NONE = "none"
    THIN = "0.8mm"
    THICK = "2mm"
    REGULAR = "regular face"
    SPECIAL = "special face"


@dataclass
class Board(NamedItem):
    """Represents a board in the cabinet."""
    name: str
    width: int
    height: int
    thickness: int
    holes: list[Hole] = field(default_factory=list)
    grooves: list[Groove] = field(default_factory=list)
    banding_top: BandingType = BandingType.NONE
    banding_bottom: BandingType = BandingType.NONE
    banding_left: BandingType = BandingType.NONE
    banding_right: BandingType = BandingType.NONE

    def get_name(self) -> str:
        return self.name


@dataclass
class LocatedBoard(ABC):
    board: Board
    location: Point
    reference_rotation: Rotation = field(default_factory=lambda: Rotation.NONE)
    reference_location: Point = field(default_factory=lambda: Point(0, 0, 0))

    def get_name(self) -> str:
        return self.board.name

    def get_shape(self) -> Box:
        return self.get_unreferenced_shape().rotate(self.reference_location, self.reference_rotation)

    @abstractmethod
    def get_unreferenced_shape(self) -> Box:
        pass

    @abstractmethod
    def get_groove_boxes(self) -> list[Box]:
        pass

    def colides_with_board(self, other: 'LocatedBoard') -> bool:
        main_box = self.get_shape()
        other_main_box = other.get_shape()
        main_boxes_intersection = main_box.intersection_with_box(other_main_box)
        if main_boxes_intersection.is_empty():
            return False
        for groove_box in self.get_groove_boxes() + other.get_groove_boxes():
            if groove_box.fully_contains(main_boxes_intersection):
                return False
        return not main_boxes_intersection.is_empty()

    def colides_with_dowel(self, dowel: LocatedDowel) -> bool:
        main_box = self.get_shape()
        dowel_cylinder = dowel.get_cylinder()
        intersection = dowel_cylinder.intersection_with_box(main_box)
        if intersection.is_empty():
            return False
        for hole_cylinder in self.get_hole_cylinders():
            if hole_cylinder.fully_contains(intersection):
                return False
        return True

    @abstractmethod
    def get_hole_cylinders(self) -> list[Cylinder]:
        pass

    @abstractmethod
    def get_unreferenced_side(self, side: Side) -> tuple[Surface, BandingType]:
        pass

    def get_side(self, side: Side) -> tuple[Surface, BandingType]:
        plane, banding_type = self.get_unreferenced_side(side)
        return (plane.rotate_and_shift(self.reference_rotation, self.reference_location), banding_type)

    def unlocate(self, point: Point) -> Point:
        return (point-self.reference_location).rotate(self.reference_rotation.inverse())-self.location

    @abstractmethod
    def add_hole_at_position(self, diameter: int, depth: int, side: Side, position: Point) -> None:
        pass


class FlatLocatedBoard(LocatedBoard):
    def get_size_in_dimension(self, dimension: Dimension) -> int:
        if dimension == Dimension.X:
            return self.board.width
        elif dimension == Dimension.Y:
            return self.board.height
        elif dimension == Dimension.Z:
            return self.board.thickness
        else:
            raise ValueError("Invalid dimension")

    def get_unreferenced_shape(self) -> Box:
        return Box(0, self.board.width, 0, self.board.height, 0, self.board.thickness) + self.location

    def get_groove_boxes(self) -> list[Box]:
        result: list[Box] = []
        x1: float
        x2: float
        y1: float
        y2: float
        z1: float
        z2: float
        for groove in self.board.grooves:
            if groove.face == Side.FRONT:
                if groove.edge == Side.TOP:
                    x1 = max(groove.distance_1_if_not_through, 0)
                    x2 = self.board.width - max(groove.distance_2_if_not_through, 0)
                    y1 = self.board.height - groove.distance_from_edge - groove.width
                    y2 = self.board.height - groove.distance_from_edge
                    z1 = self.board.thickness - groove.depth
                    z2 = self.board.thickness
                elif groove.edge == Side.BOTTOM:
                    x1 = max(groove.distance_1_if_not_through, 0)
                    x2 = self.board.width - max(groove.distance_2_if_not_through, 0)
                    y1 = groove.distance_from_edge
                    y2 = groove.distance_from_edge + groove.width
                    z1 = self.board.thickness - groove.depth
                    z2 = self.board.thickness
                elif groove.edge == Side.LEFT:
                    x1 = groove.distance_from_edge
                    x2 = groove.distance_from_edge + groove.width
                    y1 = max(groove.distance_2_if_not_through, 0)
                    y2 = self.board.height - max(groove.distance_1_if_not_through, 0)
                    z1 = self.board.thickness - groove.depth
                    z2 = self.board.thickness
                elif groove.edge == Side.RIGHT:
                    x1 = self.board.width - groove.distance_from_edge - groove.width
                    x2 = self.board.width - groove.distance_from_edge
                    y1 = max(groove.distance_2_if_not_through, 0)
                    y2 = self.board.height - max(groove.distance_1_if_not_through, 0)
                    z1 = self.board.thickness - groove.depth
                    z2 = self.board.thickness
                else:
                    raise ValueError("Invalid groove edge")
            elif groove.face == Side.BACK:
                if groove.edge == Side.TOP:
                    x1 = max(groove.distance_1_if_not_through, 0)
                    x2 = self.board.width - max(groove.distance_2_if_not_through, 0)
                    y1 = self.board.height - groove.distance_from_edge - groove.width
                    y2 = self.board.height - groove.distance_from_edge
                    z1 = 0
                    z2 = groove.depth
                elif groove.edge == Side.BOTTOM:
                    x1 = max(groove.distance_1_if_not_through, 0)
                    x2 = self.board.width - max(groove.distance_2_if_not_through, 0)
                    y1 = groove.distance_from_edge
                    y2 = groove.distance_from_edge + groove.width
                    z1 = 0
                    z2 = groove.depth
                elif groove.edge == Side.LEFT:
                    x1 = groove.distance_from_edge
                    x2 = groove.distance_from_edge + groove.width
                    y1 = max(groove.distance_2_if_not_through, 0)
                    y2 = self.board.height - max(groove.distance_1_if_not_through, 0)
                    z1 = 0
                    z2 = groove.depth
                elif groove.edge == Side.RIGHT:
                    x1 = self.board.width - groove.distance_from_edge - groove.width
                    x2 = self.board.width - groove.distance_from_edge
                    y1 = max(groove.distance_2_if_not_through, 0)
                    y2 = self.board.height - max(groove.distance_1_if_not_through, 0)
                    z1 = 0
                    z2 = groove.depth
                else:
                    raise ValueError("Invalid groove edge")
            else:
                raise ValueError("Invalid groove face")
            result.append((Box(x1, x2, y1, y2, z1, z2)+self.location).rotate(
                self.reference_location, self.reference_rotation))
        return result

    def get_hole_cylinders(self) -> list[Cylinder]:
        result: list[Cylinder] = []
        x: float
        y: float
        z: float
        dimension: Dimension
        for hole in self.board.holes:
            depth = hole.depth
            if depth == -1:
                depth = self.board.thickness
            if isinstance(hole, FrontHole):
                x = hole.position_from_left
                y = hole.position_from_bottom
                z = self.board.thickness - depth
                dimension = Dimension.Z
            elif isinstance(hole, BackHole):
                x = hole.position_from_left
                y = hole.position_from_bottom
                z = 0
                dimension = Dimension.Z
            elif isinstance(hole, LeftHole):
                x = 0
                y = hole.position_from_bottom
                z = self.board.thickness/2
                dimension = Dimension.X
            elif isinstance(hole, RightHole):
                x = self.board.width - depth
                y = hole.position_from_bottom
                z = self.board.thickness/2
                dimension = Dimension.X
            elif isinstance(hole, TopHole):
                x = hole.position_from_left
                y = self.board.height - depth
                z = self.board.thickness/2
                dimension = Dimension.Y
            elif isinstance(hole, BottomHole):
                x = hole.position_from_left
                y = 0
                z = self.board.thickness/2
                dimension = Dimension.Y
            else:
                raise ValueError("Invalid hole type")
            result.append((Cylinder.cylinder_factory(hole.diameter/2, depth, Point(x, y, z), dimension) + self.location
                           ).rotate(self.reference_location, self.reference_rotation))
        return result

    def get_unreferenced_side(self, side: Side) -> tuple[Surface, BandingType]:
        main_box = self.get_unreferenced_shape()
        if side == Side.FRONT:
            start_point = Point(main_box.x_min, main_box.y_min, main_box.z_max)
            end_point = Point(main_box.x_max, main_box.y_max, main_box.z_max)
            normal = Dimension.Z
            depth = main_box.z_max - main_box.z_min
            surface = Surface(start_point, end_point, normal, depth)
            return (surface, BandingType.REGULAR)
        elif side == Side.BACK:
            start_point = Point(main_box.x_min, main_box.y_min, main_box.z_min)
            end_point = Point(main_box.x_max, main_box.y_max, main_box.z_min)
            normal = Dimension.Z
            depth = main_box.z_max - main_box.z_min
            surface = Surface(start_point, end_point, normal, -depth)
            return (surface, BandingType.REGULAR)
        elif side == Side.TOP:
            start_point = Point(main_box.x_min, main_box.y_max, main_box.z_min)
            end_point = Point(main_box.x_max, main_box.y_max, main_box.z_max)
            normal = Dimension.Y
            depth = main_box.z_max - main_box.z_min
            surface = Surface(start_point, end_point, normal, depth)
            return (surface, self.board.banding_top)
        elif side == Side.BOTTOM:
            start_point = Point(main_box.x_min, main_box.y_min, main_box.z_min)
            end_point = Point(main_box.x_max, main_box.y_min, main_box.z_max)
            normal = Dimension.Y
            depth = main_box.z_max - main_box.z_min
            surface = Surface(start_point, end_point, normal, -depth)
            return (surface, self.board.banding_bottom)
        elif side == Side.LEFT:
            start_point = Point(main_box.x_min, main_box.y_min, main_box.z_min)
            end_point = Point(main_box.x_min, main_box.y_max, main_box.z_max)
            normal = Dimension.X
            depth = main_box.z_max - main_box.z_min
            surface = Surface(start_point, end_point, normal, -depth)
            return (surface, self.board.banding_left)
        elif side == Side.RIGHT:
            start_point = Point(main_box.x_max, main_box.y_min, main_box.z_min)
            end_point = Point(main_box.x_max, main_box.y_max, main_box.z_max)
            normal = Dimension.X
            depth = main_box.z_max - main_box.z_min
            surface = Surface(start_point, end_point, normal, depth)
            return (surface, self.board.banding_right)
        else:
            raise ValueError("Invalid side")

    def add_hole_at_position(self, diameter: int, depth: int, side: Side, position: Point) -> None:
        position = self.unlocate(position)
        if position.x % 1 != 0:
            raise ValueError("Position must be on a whole millimeter")
        if position.y % 1 != 0:
            raise ValueError("Position must be on a whole millimeter")
        if position.x < 0 or position.x > self.get_size_in_dimension(Dimension.X):
            raise ValueError("Position X is out of board " + self.board.name)
        if position.y < 0 or position.y > self.get_size_in_dimension(Dimension.Y):
            raise ValueError("Position Y is out of board " + self.board.name)
        if side == Side.FRONT:
            self.board.holes.append(FrontHole(diameter, depth, round(position.x), round(position.y)))
        elif side == Side.BACK:
            self.board.holes.append(BackHole(diameter, depth, round(position.x), round(position.y)))
        elif side == Side.LEFT:
            self.board.holes.append(LeftHole(diameter, depth, round(position.y)))
        elif side == Side.RIGHT:
            self.board.holes.append(RightHole(diameter, depth, round(position.y)))
        elif side == Side.TOP:
            self.board.holes.append(TopHole(diameter, depth, round(position.x)))
        elif side == Side.BOTTOM:
            self.board.holes.append(BottomHole(diameter, depth, round(position.x)))
        else:
            raise ValueError("Invalid side")


class SideLocatedBoard(LocatedBoard):
    def get_size_in_dimension(self, dimension: Dimension) -> int:
        if dimension == Dimension.X:
            return self.board.thickness
        elif dimension == Dimension.Y:
            return self.board.width
        elif dimension == Dimension.Z:
            return self.board.height
        else:
            raise ValueError("Invalid dimension")

    def get_unreferenced_shape(self) -> Box:
        return Box(0, self.board.thickness, 0, self.board.width, 0, self.board.height) + self.location

    def get_groove_boxes(self) -> list[Box]:
        result: list[Box] = []
        x1: float
        x2: float
        y1: float
        y2: float
        z1: float
        z2: float
        for groove in self.board.grooves:
            if groove.face == Side.FRONT:
                if groove.edge == Side.TOP:
                    x1 = self.board.thickness - groove.depth
                    x2 = self.board.thickness
                    y1 = max(groove.distance_1_if_not_through, 0)
                    y2 = self.board.width - max(groove.distance_2_if_not_through, 0)
                    z1 = self.board.height - groove.distance_from_edge - groove.width
                    z2 = self.board.height - groove.distance_from_edge
                elif groove.edge == Side.BOTTOM:
                    x1 = self.board.thickness - groove.depth
                    x2 = self.board.thickness
                    y1 = max(groove.distance_1_if_not_through, 0)
                    y2 = self.board.width - max(groove.distance_2_if_not_through, 0)
                    z1 = groove.distance_from_edge
                    z2 = groove.distance_from_edge + groove.width
                elif groove.edge == Side.LEFT:
                    x1 = self.board.thickness - groove.depth
                    x2 = self.board.thickness
                    y1 = groove.distance_from_edge
                    y2 = groove.distance_from_edge + groove.width
                    z1 = max(groove.distance_2_if_not_through, 0)
                    z2 = self.board.height - max(groove.distance_1_if_not_through, 0)
                elif groove.edge == Side.RIGHT:
                    x1 = self.board.thickness - groove.depth
                    x2 = self.board.thickness
                    y1 = self.board.width - groove.distance_from_edge - groove.width
                    y2 = self.board.width - groove.distance_from_edge
                    z1 = max(groove.distance_2_if_not_through, 0)
                    z2 = self.board.height - max(groove.distance_1_if_not_through, 0)
                else:
                    raise ValueError("Invalid groove edge")
            elif groove.face == Side.BACK:
                if groove.edge == Side.TOP:
                    x1 = 0
                    x2 = groove.depth
                    y1 = max(groove.distance_1_if_not_through, 0)
                    y2 = self.board.width - max(groove.distance_2_if_not_through, 0)
                    z1 = self.board.height - groove.distance_from_edge - groove.width
                    z2 = self.board.height - groove.distance_from_edge
                elif groove.edge == Side.BOTTOM:
                    x1 = 0
                    x2 = groove.depth
                    y1 = max(groove.distance_1_if_not_through, 0)
                    y2 = self.board.width - max(groove.distance_2_if_not_through, 0)
                    z1 = groove.distance_from_edge
                    z2 = groove.distance_from_edge + groove.width
                elif groove.edge == Side.LEFT:
                    x1 = 0
                    x2 = groove.depth
                    y1 = groove.distance_from_edge
                    y2 = groove.distance_from_edge + groove.width
                    z1 = max(groove.distance_2_if_not_through, 0)
                    z2 = self.board.height - max(groove.distance_1_if_not_through, 0)
                elif groove.edge == Side.RIGHT:
                    x1 = 0
                    x2 = groove.depth
                    y1 = self.board.width - groove.distance_from_edge - groove.width
                    y2 = self.board.width - groove.distance_from_edge
                    z1 = max(groove.distance_2_if_not_through, 0)
                    z2 = self.board.height - max(groove.distance_1_if_not_through, 0)
                else:
                    raise ValueError("Invalid groove edge")
            else:
                raise ValueError("Invalid groove face")
            result.append((Box(x1, x2, y1, y2, z1, z2)+self.location).rotate(
                self.reference_location, self.reference_rotation))
        return result

    def get_hole_cylinders(self) -> list[Cylinder]:
        result: list[Cylinder] = []
        x: float
        y: float
        z: float
        dimension: Dimension
        for hole in self.board.holes:
            depth = hole.depth
            if depth == -1:
                depth = self.board.thickness
            if isinstance(hole, FrontHole):
                x = self.board.thickness - depth
                y = hole.position_from_left
                z = hole.position_from_bottom
                dimension = Dimension.X
            elif isinstance(hole, BackHole):
                x = 0
                y = hole.position_from_left
                z = hole.position_from_bottom
                dimension = Dimension.X
            elif isinstance(hole, LeftHole):
                x = self.board.thickness/2
                y = 0
                z = hole.position_from_bottom
                dimension = Dimension.Y
            elif isinstance(hole, RightHole):
                x = self.board.thickness/2
                y = self.board.width - depth
                z = hole.position_from_bottom
                dimension = Dimension.Y
            elif isinstance(hole, TopHole):
                x = self.board.thickness/2
                y = hole.position_from_left
                z = self.board.height - depth
                dimension = Dimension.Z
            elif isinstance(hole, BottomHole):
                x = self.board.thickness/2
                y = hole.position_from_left
                z = 0
                dimension = Dimension.Z
            else:
                raise ValueError("Invalid hole type")
            result.append((Cylinder.cylinder_factory(hole.diameter/2, depth, Point(x, y, z), dimension) + self.location
                           ).rotate(self.reference_location, self.reference_rotation))
        return result

    def get_unreferenced_side(self, side: Side) -> tuple[Surface, BandingType]:
        main_box = self.get_unreferenced_shape()
        if side == Side.FRONT:
            start_point = Point(main_box.x_max, main_box.y_min, main_box.z_min)
            end_point = Point(main_box.x_max, main_box.y_max, main_box.z_max)
            normal = Dimension.X
            depth = main_box.x_max - main_box.x_min
            surface = Surface(start_point, end_point, normal, depth)
            return (surface, BandingType.REGULAR)
        elif side == Side.BACK:
            start_point = Point(main_box.x_min, main_box.y_min, main_box.z_min)
            end_point = Point(main_box.x_min, main_box.y_max, main_box.z_max)
            normal = Dimension.X
            depth = main_box.x_max - main_box.x_min
            surface = Surface(start_point, end_point, normal, -depth)
            return (surface, BandingType.REGULAR)
        elif side == Side.TOP:
            start_point = Point(main_box.x_min, main_box.y_min, main_box.z_max)
            end_point = Point(main_box.x_max, main_box.y_max, main_box.z_max)
            normal = Dimension.Z
            depth = main_box.z_max - main_box.z_min
            surface = Surface(start_point, end_point, normal, depth)
            return (surface, self.board.banding_top)
        elif side == Side.BOTTOM:
            start_point = Point(main_box.x_min, main_box.y_min, main_box.z_min)
            end_point = Point(main_box.x_max, main_box.y_max, main_box.z_min)
            normal = Dimension.Z
            depth = main_box.z_max - main_box.z_min
            surface = Surface(start_point, end_point, normal, -depth)
            return (surface, self.board.banding_bottom)
        elif side == Side.LEFT:
            start_point = Point(main_box.x_min, main_box.y_min, main_box.z_min)
            end_point = Point(main_box.x_max, main_box.y_min, main_box.z_max)
            normal = Dimension.Y
            depth = main_box.y_max - main_box.y_min
            surface = Surface(start_point, end_point, normal, -depth)
            return (surface, self.board.banding_left)
        elif side == Side.RIGHT:
            start_point = Point(main_box.x_min, main_box.y_max, main_box.z_min)
            end_point = Point(main_box.x_max, main_box.y_max, main_box.z_max)
            normal = Dimension.Y
            depth = main_box.y_max - main_box.y_min
            surface = Surface(start_point, end_point, normal, depth)
            return (surface, self.board.banding_right)
        else:
            raise ValueError("Invalid side")

    def add_hole_at_position(self, diameter: int, depth: int, side: Side, position: Point) -> None:
        position = self.unlocate(position)
        if position.z % 1 != 0:
            raise ValueError("Position must be on a whole millimeter")
        if position.y % 1 != 0:
            raise ValueError("Position must be on a whole millimeter")
        if position.z < 0 or position.z > self.get_size_in_dimension(Dimension.Z):
            raise ValueError("Position Z is out of board " + self.board.name)
        if position.y < 0 or position.y > self.get_size_in_dimension(Dimension.Y):
            raise ValueError("Position Y is out of board " + self.board.name)
        if side == Side.FRONT:
            self.board.holes.append(FrontHole(diameter, depth, round(position.y), round(position.z)))
        elif side == Side.BACK:
            self.board.holes.append(BackHole(diameter, depth, round(position.y), round(position.z)))
        elif side == Side.LEFT:
            self.board.holes.append(LeftHole(diameter, depth, round(position.z)))
        elif side == Side.RIGHT:
            self.board.holes.append(RightHole(diameter, depth, round(position.z)))
        elif side == Side.TOP:
            self.board.holes.append(TopHole(diameter, depth, round(position.y)))
        elif side == Side.BOTTOM:
            self.board.holes.append(BottomHole(diameter, depth, round(position.y)))
        else:
            raise ValueError("Invalid side")


class FrontLocatedBoard(LocatedBoard):
    def get_size_in_dimension(self, dimension: Dimension) -> int:
        if dimension == Dimension.X:
            return self.board.width
        elif dimension == Dimension.Y:
            return self.board.thickness
        elif dimension == Dimension.Z:
            return self.board.height
        else:
            raise ValueError("Invalid dimension")

    def get_unreferenced_shape(self) -> Box:
        return Box(0, self.board.width, 0, self.board.thickness, 0, self.board.height) + self.location

    def get_groove_boxes(self) -> list[Box]:
        result: list[Box] = []
        x1: float
        x2: float
        y1: float
        y2: float
        z1: float
        z2: float
        for groove in self.board.grooves:
            if groove.face == Side.FRONT:
                if groove.edge == Side.TOP:
                    x1 = float(max(groove.distance_1_if_not_through, 0))
                    x2 = float(self.board.width - max(groove.distance_2_if_not_through, 0))
                    y1 = 0.0
                    y2 = float(groove.depth)
                    z1 = float(self.board.height - groove.distance_from_edge - groove.width)
                    z2 = float(self.board.height - groove.distance_from_edge)
                elif groove.edge == Side.BOTTOM:
                    x1 = max(groove.distance_1_if_not_through, 0)
                    x2 = self.board.width - max(groove.distance_2_if_not_through, 0)
                    y1 = 0
                    y2 = groove.depth
                    z1 = groove.distance_from_edge
                    z2 = groove.width + groove.distance_from_edge
                elif groove.edge == Side.LEFT:
                    x1 = groove.distance_from_edge
                    x2 = groove.distance_from_edge + groove.width
                    y1 = 0
                    y2 = groove.depth
                    z1 = max(groove.distance_2_if_not_through, 0)
                    z2 = self.board.height - max(groove.distance_1_if_not_through, 0)
                elif groove.edge == Side.RIGHT:
                    x1 = self.board.width - groove.distance_from_edge - groove.width
                    x2 = self.board.width - groove.distance_from_edge
                    y1 = 0
                    y2 = groove.depth
                    z1 = max(groove.distance_2_if_not_through, 0)
                    z2 = self.board.height - max(groove.distance_1_if_not_through, 0)
                else:
                    raise ValueError("Invalid groove edge")
            elif groove.face == Side.BACK:
                if groove.edge == Side.TOP:
                    x1 = max(groove.distance_1_if_not_through, 0)
                    x2 = self.board.width - max(groove.distance_2_if_not_through, 0)
                    y1 = self.board.thickness - groove.depth
                    y2 = self.board.thickness
                    z1 = self.board.height - groove.distance_from_edge - groove.width
                    z2 = self.board.height - groove.distance_from_edge
                elif groove.edge == Side.BOTTOM:
                    x1 = max(groove.distance_1_if_not_through, 0)
                    x2 = self.board.width - max(groove.distance_2_if_not_through, 0)
                    y1 = self.board.thickness - groove.depth
                    y2 = self.board.thickness
                    z1 = groove.distance_from_edge
                    z2 = groove.distance_from_edge + groove.width
                elif groove.edge == Side.LEFT:
                    x1 = groove.distance_from_edge
                    x2 = groove.distance_from_edge + groove.width
                    y1 = self.board.thickness - groove.depth
                    y2 = self.board.thickness
                    z1 = max(groove.distance_2_if_not_through, 0)
                    z2 = self.board.height - max(groove.distance_1_if_not_through, 0)
                elif groove.edge == Side.RIGHT:
                    x1 = self.board.width - groove.distance_from_edge - groove.width
                    x2 = self.board.width - groove.distance_from_edge
                    y1 = self.board.thickness - groove.depth
                    y2 = self.board.thickness
                    z1 = max(groove.distance_2_if_not_through, 0)
                    z2 = self.board.height - max(groove.distance_1_if_not_through, 0)
                else:
                    raise ValueError("Invalid groove edge")
            else:
                raise ValueError("Invalid groove face")
            result.append((Box(x1, x2, y1, y2, z1, z2)+self.location).rotate(
                self.reference_location, self.reference_rotation))
        return result

    def get_hole_cylinders(self) -> list[Cylinder]:
        result: list[Cylinder] = []
        x: float
        y: float
        z: float
        dimension: Dimension
        for hole in self.board.holes:
            depth = hole.depth
            if depth == -1:
                depth = self.board.thickness
            if isinstance(hole, FrontHole):
                x = float(hole.position_from_left)
                y = 0.0
                z = float(hole.position_from_bottom)
                dimension = Dimension.Y
            elif isinstance(hole, BackHole):
                x = hole.position_from_left
                y = self.board.thickness - depth
                z = hole.position_from_bottom
                dimension = Dimension.Y
            elif isinstance(hole, LeftHole):
                x = 0
                y = self.board.thickness/2
                z = hole.position_from_bottom
                dimension = Dimension.X
            elif isinstance(hole, RightHole):
                x = self.board.width - depth
                y = self.board.thickness/2
                z = hole.position_from_bottom
                dimension = Dimension.X
            elif isinstance(hole, TopHole):
                x = hole.position_from_left
                y = self.board.thickness/2
                z = self.board.height - depth
                dimension = Dimension.Z
            elif isinstance(hole, BottomHole):
                x = hole.position_from_left
                y = self.board.thickness/2
                z = 0
                dimension = Dimension.Z
            else:
                raise ValueError("Invalid hole type")
            result.append((Cylinder.cylinder_factory(hole.diameter/2, depth, Point(x, y, z), dimension) + self.location
                           ).rotate(self.reference_location, self.reference_rotation))
        return result

    def get_unreferenced_side(self, side: Side) -> tuple[Surface, BandingType]:
        main_box = self.get_unreferenced_shape()
        if side == Side.FRONT:
            start_point = Point(main_box.x_min, main_box.y_min, main_box.z_min)
            end_point = Point(main_box.x_max, main_box.y_min, main_box.z_max)
            normal = Dimension.Y
            depth = main_box.y_max - main_box.y_min
            surface = Surface(start_point, end_point, normal, -depth)
            return (surface, BandingType.REGULAR)
        elif side == Side.BACK:
            start_point = Point(main_box.x_min, main_box.y_max, main_box.z_min)
            end_point = Point(main_box.x_max, main_box.y_max, main_box.z_max)
            normal = Dimension.Y
            depth = main_box.y_max - main_box.y_min
            surface = Surface(start_point, end_point, normal, depth)
            return (surface, BandingType.REGULAR)
        elif side == Side.TOP:
            start_point = Point(main_box.x_min, main_box.y_min, main_box.z_max)
            end_point = Point(main_box.x_max, main_box.y_max, main_box.z_max)
            normal = Dimension.Z
            depth = main_box.z_max - main_box.z_min
            surface = Surface(start_point, end_point, normal, depth)
            return (surface, self.board.banding_top)
        elif side == Side.BOTTOM:
            start_point = Point(main_box.x_min, main_box.y_min, main_box.z_min)
            end_point = Point(main_box.x_max, main_box.y_max, main_box.z_min)
            normal = Dimension.Z
            depth = main_box.z_max - main_box.z_min
            surface = Surface(start_point, end_point, normal, -depth)
            return (surface, self.board.banding_bottom)
        elif side == Side.LEFT:
            start_point = Point(main_box.x_min, main_box.y_min, main_box.z_min)
            end_point = Point(main_box.x_min, main_box.y_max, main_box.z_max)
            normal = Dimension.X
            depth = main_box.x_max - main_box.x_min
            surface = Surface(start_point, end_point, normal, -depth)
            return (surface, self.board.banding_left)
        elif side == Side.RIGHT:
            start_point = Point(main_box.x_max, main_box.y_min, main_box.z_min)
            end_point = Point(main_box.x_max, main_box.y_max, main_box.z_max)
            normal = Dimension.X
            depth = main_box.x_max - main_box.x_min
            surface = Surface(start_point, end_point, normal, depth)
            return (surface, self.board.banding_right)
        else:
            raise ValueError("Invalid side")

    def add_hole_at_position(self, diameter: int, depth: int, side: Side, position: Point) -> None:
        position = self.unlocate(position)
        if position.x % 1 != 0:
            raise ValueError("Position must be on a whole millimeter")
        if position.z % 1 != 0:
            raise ValueError("Position must be on a whole millimeter")
        if position.x < 0 or position.x > self.get_size_in_dimension(Dimension.X):
            raise ValueError("Position X is out of board " + self.board.name)
        if position.z < 0 or position.z > self.get_size_in_dimension(Dimension.Z):
            raise ValueError("Position Z is out of board " + self.board.name)
        if side == Side.FRONT:
            self.board.holes.append(FrontHole(diameter, depth, round(position.x), round(position.z)))
        elif side == Side.BACK:
            self.board.holes.append(BackHole(diameter, depth, round(position.x), round(position.z)))
        elif side == Side.LEFT:
            self.board.holes.append(LeftHole(diameter, depth, round(position.z)))
        elif side == Side.RIGHT:
            self.board.holes.append(RightHole(diameter, depth, round(position.z)))
        elif side == Side.TOP:
            self.board.holes.append(TopHole(diameter, depth, round(position.x)))
        elif side == Side.BOTTOM:
            self.board.holes.append(BottomHole(diameter, depth, round(position.x)))
        else:
            raise ValueError("Invalid side")
