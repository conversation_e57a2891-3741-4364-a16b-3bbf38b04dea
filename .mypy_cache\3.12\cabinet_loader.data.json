{".class": "MypyFile", "_fullname": "cabinet_loader", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BackHole": {".class": "SymbolTableNode", "cross_ref": "board_hole.BackHole", "kind": "Gdef"}, "BandingType": {".class": "SymbolTableNode", "cross_ref": "board.BandingType", "kind": "Gdef"}, "Board": {".class": "SymbolTableNode", "cross_ref": "board.Board", "kind": "Gdef"}, "BoardJoin_L": {".class": "SymbolTableNode", "cross_ref": "board_join_<PERSON>.<PERSON>n_L", "kind": "Gdef"}, "BoardJoin_L_cam_lock": {".class": "SymbolTableNode", "cross_ref": "board_join_<PERSON>.<PERSON>_L_cam_lock", "kind": "Gdef"}, "BoardJoin_L_confirmat_7x50": {".class": "SymbolTableNode", "cross_ref": "board_join_<PERSON><PERSON>_L_confirmat_7x50", "kind": "Gdef"}, "BoardJoin_L_dowel": {".class": "SymbolTableNode", "cross_ref": "board_join_<PERSON>.<PERSON>_<PERSON>_dowel", "kind": "Gdef"}, "BottomHole": {".class": "SymbolTableNode", "cross_ref": "board_hole.BottomHole", "kind": "Gdef"}, "Box": {".class": "SymbolTableNode", "cross_ref": "box.Box", "kind": "Gdef"}, "Cabinet": {".class": "SymbolTableNode", "cross_ref": "cabinet.Cabinet", "kind": "Gdef"}, "CabinetLoader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cabinet_loader.CabinetLoader", "name": "<PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cabinet_loader.CabinetLoader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cabinet_loader", "mro": ["cabinet_loader.CabinetLoader", "builtins.object"], "names": {".class": "SymbolTable", "_create_L_join_from_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "cabinet", "name_prefix", "L_join_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cabinet_loader.CabinetLoader._create_L_join_from_data", "name": "_create_L_join_from_data", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "cabinet", "name_prefix", "L_join_data"], "arg_types": ["cabinet_loader.CabinetLoader", "cabinet.Cabinet", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_L_join_from_data of CabinetLoader", "ret_type": "board_join_<PERSON>.<PERSON>n_L", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_board_from_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "name_prefix", "board_data", "evaluator", "rotation", "reference_location"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cabinet_loader.CabinetLoader._create_board_from_data", "name": "_create_board_from_data", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "name_prefix", "board_data", "evaluator", "rotation", "reference_location"], "arg_types": ["cabinet_loader.CabinetLoader", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "cabinet_loader.Evaluator", "base.Rotation", "base.Point"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_board_from_data of CabinetLoader", "ret_type": "board.LocatedBoard", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_complex_spacer_from_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "name_prefix", "complex_spacer_data", "evaluator", "rotation", "reference_location"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cabinet_loader.CabinetLoader._create_complex_spacer_from_data", "name": "_create_complex_spacer_from_data", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "name_prefix", "complex_spacer_data", "evaluator", "rotation", "reference_location"], "arg_types": ["cabinet_loader.CabinetLoader", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "cabinet_loader.Evaluator", "base.Rotation", "base.Point"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_complex_spacer_from_data of CabinetLoader", "ret_type": "complex_spacer.ComplexSpacerLocated", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_dowel_from_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "name_prefix", "dowel_data", "evaluator", "rotation", "reference_location"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cabinet_loader.CabinetLoader._create_dowel_from_data", "name": "_create_dowel_from_data", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "name_prefix", "dowel_data", "evaluator", "rotation", "reference_location"], "arg_types": ["cabinet_loader.CabinetLoader", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "cabinet_loader.Evaluator", "base.Rotation", "base.Point"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_dowel_from_data of CabinetLoader", "ret_type": "dowel.LocatedDowel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_box_from_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "box_data", "evaluator"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cabinet_loader.CabinetLoader._get_box_from_data", "name": "_get_box_from_data", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "box_data", "evaluator"], "arg_types": ["cabinet_loader.CabinetLoader", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "cabinet_loader.Evaluator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_box_from_data of CabinetLoader", "ret_type": "box.Box", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_cylinder_from_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "cylinder_data", "evaluator"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cabinet_loader.CabinetLoader._get_cylinder_from_data", "name": "_get_cylinder_from_data", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "cylinder_data", "evaluator"], "arg_types": ["cabinet_loader.CabinetLoader", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "cabinet_loader.Evaluator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_cylinder_from_data of CabinetLoader", "ret_type": "cylinder.Cy<PERSON>er", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_groove_from_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["groove_data", "evaluator"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "cabinet_loader.CabinetLoader._get_groove_from_data", "name": "_get_groove_from_data", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["groove_data", "evaluator"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "cabinet_loader.Evaluator"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_groove_from_data of CabinetLoader", "ret_type": "board_groove.Groove", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "cabinet_loader.CabinetLoader._get_groove_from_data", "name": "_get_groove_from_data", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["groove_data", "evaluator"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "cabinet_loader.Evaluator"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_groove_from_data of CabinetLoader", "ret_type": "board_groove.Groove", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_hole_from_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["hole_data", "evaluator"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "cabinet_loader.CabinetLoader._get_hole_from_data", "name": "_get_hole_from_data", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["hole_data", "evaluator"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "cabinet_loader.Evaluator"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_hole_from_data of CabinetLoader", "ret_type": "board_hole.Hole", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "cabinet_loader.CabinetLoader._get_hole_from_data", "name": "_get_hole_from_data", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["hole_data", "evaluator"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "cabinet_loader.Evaluator"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_hole_from_data of CabinetLoader", "ret_type": "board_hole.Hole", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "load_from_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "file_path", "evaluator", "rotation", "reference_location", "name_prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cabinet_loader.CabinetLoader.load_from_file", "name": "load_from_file", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "file_path", "evaluator", "rotation", "reference_location", "name_prefix"], "arg_types": ["cabinet_loader.CabinetLoader", "builtins.str", {".class": "UnionType", "items": ["cabinet_loader.Evaluator", {".class": "NoneType"}], "uses_pep604_syntax": false}, "base.Rotation", "base.Point", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_from_file of CabinetLoader", "ret_type": "cabinet.Cabinet", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cabinet_loader.CabinetLoader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cabinet_loader.CabinetLoader", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ComplexSpacer": {".class": "SymbolTableNode", "cross_ref": "complex_spacer.ComplexSpacer", "kind": "Gdef"}, "ComplexSpacerLocated": {".class": "SymbolTableNode", "cross_ref": "complex_spacer.ComplexSpacerLocated", "kind": "Gdef"}, "Cylinder": {".class": "SymbolTableNode", "cross_ref": "cylinder.Cy<PERSON>er", "kind": "Gdef"}, "Dimension": {".class": "SymbolTableNode", "cross_ref": "base.Dimension", "kind": "Gdef"}, "Dowel": {".class": "SymbolTableNode", "cross_ref": "dowe<PERSON>.<PERSON>", "kind": "Gdef"}, "Evaluator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cabinet_loader.Evaluator", "name": "Evaluator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cabinet_loader.Evaluator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cabinet_loader", "mro": ["cabinet_loader.Evaluator", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cabinet_loader.Evaluator.__init__", "name": "__init__", "type": null}}, "add_variable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cabinet_loader.Evaluator.add_variable", "name": "add_variable", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "arg_types": ["cabinet_loader.Evaluator", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_variable of Evaluator", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "evaluate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cabinet_loader.Evaluator.evaluate", "name": "evaluate", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["cabinet_loader.Evaluator", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "evaluate of Evaluator", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "half_float_validator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "cabinet_loader.Evaluator.half_float_validator", "name": "half_float_validator", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["value"], "arg_types": ["builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "half_float_validator of Evaluator", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "cabinet_loader.Evaluator.half_float_validator", "name": "half_float_validator", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["value"], "arg_types": ["builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "half_float_validator of Evaluator", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_empty": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cabinet_loader.Evaluator.is_empty", "name": "is_empty", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cabinet_loader.Evaluator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_empty of Evaluator", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "safe_functions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cabinet_loader.Evaluator.safe_functions", "name": "safe_functions", "type": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "variables": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cabinet_loader.Evaluator.variables", "name": "variables", "type": {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cabinet_loader.Evaluator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cabinet_loader.Evaluator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlatLocatedBoard": {".class": "SymbolTableNode", "cross_ref": "board.FlatLocatedBoard", "kind": "Gdef"}, "FrontHole": {".class": "SymbolTableNode", "cross_ref": "board_hole.FrontHole", "kind": "Gdef"}, "FrontLocatedBoard": {".class": "SymbolTableNode", "cross_ref": "board.FrontLocatedBoard", "kind": "Gdef"}, "Groove": {".class": "SymbolTableNode", "cross_ref": "board_groove.Groove", "kind": "Gdef"}, "GrooveDepth": {".class": "SymbolTableNode", "cross_ref": "board_groove.GrooveDepth", "kind": "Gdef"}, "GrooveDistanceFromEdge": {".class": "SymbolTableNode", "cross_ref": "board_groove.GrooveDistanceFromEdge", "kind": "Gdef"}, "GrooveWidth": {".class": "SymbolTableNode", "cross_ref": "board_groove.GrooveWidth", "kind": "Gdef"}, "Hole": {".class": "SymbolTableNode", "cross_ref": "board_hole.Hole", "kind": "Gdef"}, "LeftHole": {".class": "SymbolTableNode", "cross_ref": "board_hole.LeftHole", "kind": "Gdef"}, "LocatedBoard": {".class": "SymbolTableNode", "cross_ref": "board.LocatedBoard", "kind": "Gdef"}, "LocatedDowel": {".class": "SymbolTableNode", "cross_ref": "dowel.LocatedDowel", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Orientation": {".class": "SymbolTableNode", "cross_ref": "base.Orientation", "kind": "Gdef"}, "Point": {".class": "SymbolTableNode", "cross_ref": "base.Point", "kind": "Gdef"}, "RightHole": {".class": "SymbolTableNode", "cross_ref": "board_hole.RightHole", "kind": "Gdef"}, "Rotation": {".class": "SymbolTableNode", "cross_ref": "base.Rotation", "kind": "Gdef"}, "Side": {".class": "SymbolTableNode", "cross_ref": "base.Side", "kind": "Gdef"}, "SideLocatedBoard": {".class": "SymbolTableNode", "cross_ref": "board.SideLocatedBoard", "kind": "Gdef"}, "TopHole": {".class": "SymbolTableNode", "cross_ref": "board_hole.TopHole", "kind": "Gdef"}, "XLocatedDowel": {".class": "SymbolTableNode", "cross_ref": "dowel.XLocatedDowel", "kind": "Gdef"}, "YLocatedDowel": {".class": "SymbolTableNode", "cross_ref": "dowel.YLocatedDowel", "kind": "Gdef"}, "ZLocatedDowel": {".class": "SymbolTableNode", "cross_ref": "dowel.ZLocatedDowel", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cabinet_loader.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cabinet_loader.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cabinet_loader.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cabinet_loader.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cabinet_loader.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cabinet_loader.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "cabinet": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "cabinet_loader.cabinet", "name": "cabinet", "type": "cabinet.Cabinet"}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Roaming\\FreeCAD\\Mod\\CabinetPlanner\\src\\cabinet_loader.py"}