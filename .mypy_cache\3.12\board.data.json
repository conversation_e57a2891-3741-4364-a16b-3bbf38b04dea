{".class": "MypyFile", "_fullname": "board", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ABC": {".class": "SymbolTableNode", "cross_ref": "abc.ABC", "kind": "Gdef"}, "BackHole": {".class": "SymbolTableNode", "cross_ref": "board_hole.BackHole", "kind": "Gdef"}, "BandingType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "board.BandingType", "name": "BandingType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "board.BandingType", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "board", "mro": ["board.BandingType", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "NONE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "board.BandingType.NONE", "name": "NONE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "none"}, "type_ref": "builtins.str"}}}, "REGULAR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "board.BandingType.REGULAR", "name": "REGULAR", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "regular face"}, "type_ref": "builtins.str"}}}, "SPECIAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "board.BandingType.SPECIAL", "name": "SPECIAL", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "special face"}, "type_ref": "builtins.str"}}}, "THICK": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "board.BandingType.THICK", "name": "THICK", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "2mm"}, "type_ref": "builtins.str"}}}, "THIN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "board.BandingType.THIN", "name": "THIN", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "0.8mm"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "board.BandingType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "board.BandingType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Board": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["base.NamedItem"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "board.Board", "name": "Board", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "board.Board", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 25, "name": "name", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 26, "name": "width", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 27, "name": "height", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 28, "name": "thickness", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 29, "name": "holes", "type": {".class": "Instance", "args": ["board_hole.Hole"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 30, "name": "grooves", "type": {".class": "Instance", "args": ["board_groove.Groove"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 31, "name": "banding_top", "type": "board.BandingType"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 32, "name": "banding_bottom", "type": "board.BandingType"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 33, "name": "banding_left", "type": "board.BandingType"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 34, "name": "banding_right", "type": "board.BandingType"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "board", "mro": ["board.Board", "base.NamedItem", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "board.Board.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "width", "height", "thickness", "holes", "grooves", "banding_top", "banding_bottom", "banding_left", "banding_right"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "board.Board.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "width", "height", "thickness", "holes", "grooves", "banding_top", "banding_bottom", "banding_left", "banding_right"], "arg_types": ["board.Board", "builtins.str", "builtins.int", "builtins.int", "builtins.int", {".class": "Instance", "args": ["board_hole.Hole"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["board_groove.Groove"], "extra_attrs": null, "type_ref": "builtins.list"}, "board.BandingType", "board.BandingType", "board.BandingType", "board.BandingType"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Board", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "board.Board.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "name"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "width"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "height"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "thickness"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "holes"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "grooves"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "banding_top"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "banding_bottom"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "banding_left"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "banding_right"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["name", "width", "height", "thickness", "holes", "grooves", "banding_top", "banding_bottom", "banding_left", "banding_right"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "board.Board.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["name", "width", "height", "thickness", "holes", "grooves", "banding_top", "banding_bottom", "banding_left", "banding_right"], "arg_types": ["builtins.str", "builtins.int", "builtins.int", "builtins.int", {".class": "Instance", "args": ["board_hole.Hole"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["board_groove.Groove"], "extra_attrs": null, "type_ref": "builtins.list"}, "board.BandingType", "board.BandingType", "board.BandingType", "board.BandingType"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of Board", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "board.Board.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["name", "width", "height", "thickness", "holes", "grooves", "banding_top", "banding_bottom", "banding_left", "banding_right"], "arg_types": ["builtins.str", "builtins.int", "builtins.int", "builtins.int", {".class": "Instance", "args": ["board_hole.Hole"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["board_groove.Groove"], "extra_attrs": null, "type_ref": "builtins.list"}, "board.BandingType", "board.BandingType", "board.BandingType", "board.BandingType"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of Board", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "banding_bottom": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "board.Board.banding_bottom", "name": "banding_bottom", "type": "board.BandingType"}}, "banding_left": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "board.Board.banding_left", "name": "banding_left", "type": "board.BandingType"}}, "banding_right": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "board.Board.banding_right", "name": "banding_right", "type": "board.BandingType"}}, "banding_top": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "board.Board.banding_top", "name": "banding_top", "type": "board.BandingType"}}, "get_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "board.Board.get_name", "name": "get_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["board.Board"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_name of Board", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "grooves": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "board.Board.grooves", "name": "grooves", "type": {".class": "Instance", "args": ["board_groove.Groove"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "height": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "board.Board.height", "name": "height", "type": "builtins.int"}}, "holes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "board.Board.holes", "name": "holes", "type": {".class": "Instance", "args": ["board_hole.Hole"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "board.Board.name", "name": "name", "type": "builtins.str"}}, "thickness": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "board.Board.thickness", "name": "thickness", "type": "builtins.int"}}, "width": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "board.Board.width", "name": "width", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "board.Board.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "board.Board", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BottomHole": {".class": "SymbolTableNode", "cross_ref": "board_hole.BottomHole", "kind": "Gdef"}, "Box": {".class": "SymbolTableNode", "cross_ref": "box.Box", "kind": "Gdef"}, "Cylinder": {".class": "SymbolTableNode", "cross_ref": "cylinder.Cy<PERSON>er", "kind": "Gdef"}, "Dimension": {".class": "SymbolTableNode", "cross_ref": "base.Dimension", "kind": "Gdef"}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef"}, "FlatLocatedBoard": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["board.LocatedBoard"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "board.FlatLocatedBoard", "name": "FlatLocatedBoard", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "board.FlatLocatedBoard", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "board", "mro": ["board.FlatLocatedBoard", "board.LocatedBoard", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "add_hole_at_position": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "diameter", "depth", "side", "position"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "board.FlatLocatedBoard.add_hole_at_position", "name": "add_hole_at_position", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "diameter", "depth", "side", "position"], "arg_types": ["board.FlatLocatedBoard", "builtins.int", "builtins.int", "base.Side", "base.Point"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_hole_at_position of FlatLocatedBoard", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_groove_boxes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "board.FlatLocatedBoard.get_groove_boxes", "name": "get_groove_boxes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["board.FlatLocatedBoard"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_groove_boxes of FlatLocatedBoard", "ret_type": {".class": "Instance", "args": ["box.Box"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_hole_cylinders": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "board.FlatLocatedBoard.get_hole_cylinders", "name": "get_hole_cylinders", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["board.FlatLocatedBoard"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_hole_cylinders of FlatLocatedBoard", "ret_type": {".class": "Instance", "args": ["cylinder.Cy<PERSON>er"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_size_in_dimension": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dimension"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "board.FlatLocatedBoard.get_size_in_dimension", "name": "get_size_in_dimension", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dimension"], "arg_types": ["board.FlatLocatedBoard", "base.Dimension"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_size_in_dimension of FlatLocatedBoard", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_unreferenced_shape": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "board.FlatLocatedBoard.get_unreferenced_shape", "name": "get_unreferenced_shape", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["board.FlatLocatedBoard"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_unreferenced_shape of FlatLocatedBoard", "ret_type": "box.Box", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_unreferenced_side": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "side"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "board.FlatLocatedBoard.get_unreferenced_side", "name": "get_unreferenced_side", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "side"], "arg_types": ["board.FlatLocatedBoard", "base.Side"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_unreferenced_side of FlatLocatedBoard", "ret_type": {".class": "TupleType", "implicit": false, "items": ["surface.Surface", "board.BandingType"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "board.FlatLocatedBoard.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "board.FlatLocatedBoard", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FrontHole": {".class": "SymbolTableNode", "cross_ref": "board_hole.FrontHole", "kind": "Gdef"}, "FrontLocatedBoard": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["board.LocatedBoard"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "board.FrontLocatedBoard", "name": "FrontLocatedBoard", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "board.FrontLocatedBoard", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "board", "mro": ["board.FrontLocatedBoard", "board.LocatedBoard", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "add_hole_at_position": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "diameter", "depth", "side", "position"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "board.FrontLocatedBoard.add_hole_at_position", "name": "add_hole_at_position", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "diameter", "depth", "side", "position"], "arg_types": ["board.FrontLocatedBoard", "builtins.int", "builtins.int", "base.Side", "base.Point"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_hole_at_position of FrontLocatedBoard", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_groove_boxes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "board.FrontLocatedBoard.get_groove_boxes", "name": "get_groove_boxes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["board.FrontLocatedBoard"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_groove_boxes of FrontLocatedBoard", "ret_type": {".class": "Instance", "args": ["box.Box"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_hole_cylinders": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "board.FrontLocatedBoard.get_hole_cylinders", "name": "get_hole_cylinders", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["board.FrontLocatedBoard"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_hole_cylinders of FrontLocatedBoard", "ret_type": {".class": "Instance", "args": ["cylinder.Cy<PERSON>er"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_size_in_dimension": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dimension"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "board.FrontLocatedBoard.get_size_in_dimension", "name": "get_size_in_dimension", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dimension"], "arg_types": ["board.FrontLocatedBoard", "base.Dimension"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_size_in_dimension of FrontLocatedBoard", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_unreferenced_shape": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "board.FrontLocatedBoard.get_unreferenced_shape", "name": "get_unreferenced_shape", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["board.FrontLocatedBoard"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_unreferenced_shape of FrontLocatedBoard", "ret_type": "box.Box", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_unreferenced_side": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "side"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "board.FrontLocatedBoard.get_unreferenced_side", "name": "get_unreferenced_side", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "side"], "arg_types": ["board.FrontLocatedBoard", "base.Side"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_unreferenced_side of FrontLocatedBoard", "ret_type": {".class": "TupleType", "implicit": false, "items": ["surface.Surface", "board.BandingType"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "board.FrontLocatedBoard.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "board.FrontLocatedBoard", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Groove": {".class": "SymbolTableNode", "cross_ref": "board_groove.Groove", "kind": "Gdef"}, "Hole": {".class": "SymbolTableNode", "cross_ref": "board_hole.Hole", "kind": "Gdef"}, "LeftHole": {".class": "SymbolTableNode", "cross_ref": "board_hole.LeftHole", "kind": "Gdef"}, "LocatedBoard": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["add_hole_at_position", 1], ["get_groove_boxes", 1], ["get_hole_cylinders", 1], ["get_unreferenced_shape", 1], ["get_unreferenced_side", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "board.LocatedBoard", "name": "LocatedBoard", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "board.LocatedBoard", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 42, "name": "board", "type": "board.Board"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 43, "name": "location", "type": "base.Point"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 44, "name": "reference_rotation", "type": "base.Rotation"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 45, "name": "reference_location", "type": "base.Point"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "board", "mro": ["board.LocatedBoard", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "board.LocatedBoard.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "board", "location", "reference_rotation", "reference_location"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "board.LocatedBoard.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "board", "location", "reference_rotation", "reference_location"], "arg_types": ["board.LocatedBoard", "board.Board", "base.Point", "base.Rotation", "base.Point"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LocatedBoard", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "board.LocatedBoard.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "board"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "location"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "reference_rotation"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "reference_location"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5], "arg_names": ["board", "location", "reference_rotation", "reference_location"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "board.LocatedBoard.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["board", "location", "reference_rotation", "reference_location"], "arg_types": ["board.Board", "base.Point", "base.Rotation", "base.Point"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of LocatedBoard", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "board.LocatedBoard.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["board", "location", "reference_rotation", "reference_location"], "arg_types": ["board.Board", "base.Point", "base.Rotation", "base.Point"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of LocatedBoard", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "add_hole_at_position": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "diameter", "depth", "side", "position"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "board.LocatedBoard.add_hole_at_position", "name": "add_hole_at_position", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "diameter", "depth", "side", "position"], "arg_types": ["board.LocatedBoard", "builtins.int", "builtins.int", "base.Side", "base.Point"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_hole_at_position of LocatedBoard", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "board.LocatedBoard.add_hole_at_position", "name": "add_hole_at_position", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "diameter", "depth", "side", "position"], "arg_types": ["board.LocatedBoard", "builtins.int", "builtins.int", "base.Side", "base.Point"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_hole_at_position of LocatedBoard", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "board": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "board.LocatedBoard.board", "name": "board", "type": "board.Board"}}, "colides_with_board": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "board.LocatedBoard.colides_with_board", "name": "colides_with_board", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": ["board.LocatedBoard", "board.LocatedBoard"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "colides_with_board of LocatedBoard", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "colides_with_dowel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dowel"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "board.LocatedBoard.colides_with_dowel", "name": "colides_with_dowel", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dowel"], "arg_types": ["board.LocatedBoard", "dowel.LocatedDowel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "colides_with_dowel of LocatedBoard", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_groove_boxes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "board.LocatedBoard.get_groove_boxes", "name": "get_groove_boxes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["board.LocatedBoard"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_groove_boxes of LocatedBoard", "ret_type": {".class": "Instance", "args": ["box.Box"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "board.LocatedBoard.get_groove_boxes", "name": "get_groove_boxes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["board.LocatedBoard"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_groove_boxes of LocatedBoard", "ret_type": {".class": "Instance", "args": ["box.Box"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_hole_cylinders": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "board.LocatedBoard.get_hole_cylinders", "name": "get_hole_cylinders", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["board.LocatedBoard"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_hole_cylinders of LocatedBoard", "ret_type": {".class": "Instance", "args": ["cylinder.Cy<PERSON>er"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "board.LocatedBoard.get_hole_cylinders", "name": "get_hole_cylinders", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["board.LocatedBoard"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_hole_cylinders of LocatedBoard", "ret_type": {".class": "Instance", "args": ["cylinder.Cy<PERSON>er"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "board.LocatedBoard.get_name", "name": "get_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["board.LocatedBoard"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_name of LocatedBoard", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_shape": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "board.LocatedBoard.get_shape", "name": "get_shape", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["board.LocatedBoard"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_shape of LocatedBoard", "ret_type": "box.Box", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_side": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "side"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "board.LocatedBoard.get_side", "name": "get_side", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "side"], "arg_types": ["board.LocatedBoard", "base.Side"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_side of LocatedBoard", "ret_type": {".class": "TupleType", "implicit": false, "items": ["surface.Surface", "board.BandingType"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_unreferenced_shape": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "board.LocatedBoard.get_unreferenced_shape", "name": "get_unreferenced_shape", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["board.LocatedBoard"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_unreferenced_shape of LocatedBoard", "ret_type": "box.Box", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "board.LocatedBoard.get_unreferenced_shape", "name": "get_unreferenced_shape", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["board.LocatedBoard"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_unreferenced_shape of LocatedBoard", "ret_type": "box.Box", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_unreferenced_side": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "side"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "board.LocatedBoard.get_unreferenced_side", "name": "get_unreferenced_side", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "side"], "arg_types": ["board.LocatedBoard", "base.Side"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_unreferenced_side of LocatedBoard", "ret_type": {".class": "TupleType", "implicit": false, "items": ["surface.Surface", "board.BandingType"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "board.LocatedBoard.get_unreferenced_side", "name": "get_unreferenced_side", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "side"], "arg_types": ["board.LocatedBoard", "base.Side"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_unreferenced_side of LocatedBoard", "ret_type": {".class": "TupleType", "implicit": false, "items": ["surface.Surface", "board.BandingType"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "location": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "board.LocatedBoard.location", "name": "location", "type": "base.Point"}}, "reference_location": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "board.LocatedBoard.reference_location", "name": "reference_location", "type": "base.Point"}}, "reference_rotation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "board.LocatedBoard.reference_rotation", "name": "reference_rotation", "type": "base.Rotation"}}, "unlocate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "point"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "board.LocatedBoard.unlocate", "name": "unlocate", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "point"], "arg_types": ["board.LocatedBoard", "base.Point"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unlocate of LocatedBoard", "ret_type": "base.Point", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "board.LocatedBoard.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "board.LocatedBoard", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LocatedDowel": {".class": "SymbolTableNode", "cross_ref": "dowel.LocatedDowel", "kind": "Gdef"}, "NamedItem": {".class": "SymbolTableNode", "cross_ref": "base.NamedItem", "kind": "Gdef"}, "Point": {".class": "SymbolTableNode", "cross_ref": "base.Point", "kind": "Gdef"}, "RightHole": {".class": "SymbolTableNode", "cross_ref": "board_hole.RightHole", "kind": "Gdef"}, "Rotation": {".class": "SymbolTableNode", "cross_ref": "base.Rotation", "kind": "Gdef"}, "Side": {".class": "SymbolTableNode", "cross_ref": "base.Side", "kind": "Gdef"}, "SideLocatedBoard": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["board.LocatedBoard"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "board.SideLocatedBoard", "name": "SideLocatedBoard", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "board.SideLocatedBoard", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "board", "mro": ["board.SideLocatedBoard", "board.LocatedBoard", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "add_hole_at_position": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "diameter", "depth", "side", "position"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "board.SideLocatedBoard.add_hole_at_position", "name": "add_hole_at_position", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "diameter", "depth", "side", "position"], "arg_types": ["board.SideLocatedBoard", "builtins.int", "builtins.int", "base.Side", "base.Point"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_hole_at_position of SideLocatedBoard", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_groove_boxes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "board.SideLocatedBoard.get_groove_boxes", "name": "get_groove_boxes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["board.SideLocatedBoard"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_groove_boxes of SideLocatedBoard", "ret_type": {".class": "Instance", "args": ["box.Box"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_hole_cylinders": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "board.SideLocatedBoard.get_hole_cylinders", "name": "get_hole_cylinders", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["board.SideLocatedBoard"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_hole_cylinders of SideLocatedBoard", "ret_type": {".class": "Instance", "args": ["cylinder.Cy<PERSON>er"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_size_in_dimension": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dimension"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "board.SideLocatedBoard.get_size_in_dimension", "name": "get_size_in_dimension", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dimension"], "arg_types": ["board.SideLocatedBoard", "base.Dimension"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_size_in_dimension of SideLocatedBoard", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_unreferenced_shape": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "board.SideLocatedBoard.get_unreferenced_shape", "name": "get_unreferenced_shape", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["board.SideLocatedBoard"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_unreferenced_shape of SideLocatedBoard", "ret_type": "box.Box", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_unreferenced_side": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "side"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "board.SideLocatedBoard.get_unreferenced_side", "name": "get_unreferenced_side", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "side"], "arg_types": ["board.SideLocatedBoard", "base.Side"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_unreferenced_side of SideLocatedBoard", "ret_type": {".class": "TupleType", "implicit": false, "items": ["surface.Surface", "board.BandingType"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "board.SideLocatedBoard.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "board.SideLocatedBoard", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Surface": {".class": "SymbolTableNode", "cross_ref": "surface.Surface", "kind": "Gdef"}, "TopHole": {".class": "SymbolTableNode", "cross_ref": "board_hole.TopHole", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "board.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "board.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "board.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "board.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "board.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "board.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef"}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef"}, "field": {".class": "SymbolTableNode", "cross_ref": "dataclasses.field", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Roaming\\FreeCAD\\Mod\\CabinetPlanner\\src\\board.py"}