{"data_mtime": 1762326280, "dep_lines": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 67, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["cabinet", "board", "board_hole", "board_groove", "base", "cylinder", "board_join_L", "box", "dowel", "complex_spacer", "sys", "os", "typing", "json", "builtins", "_collections_abc", "_frozen_importlib", "_io", "_typeshed", "abc", "enum", "io", "json.decoder", "types"], "hash": "37105b86ea6191838dcbc9b06893b03dea54da27", "id": "cabinet_loader", "ignore_all": false, "interface_hash": "a775181592a87fb6afecdcacf4dbe62487f2d30d", "mtime": 1762326280, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Roaming\\FreeCAD\\Mod\\CabinetPlanner\\src\\cabinet_loader.py", "plugin_data": null, "size": 16783, "suppressed": [], "version_id": "1.15.0"}